import {StatisticsOfAPIController} from "@/controller/statisticsOfAPIController";
import {EndpointType, LatestApi} from "@/entities/consolidated_data/LatestApi";
import {addLogJobExecution} from "@/lib/agenda";
import {consoleLog} from "@/lib/utils";
import {LogLevel} from "@/utils/types/logs/log";
import axios from "axios";

const {API_URL_EOD, API_KEY_EOD} = process.env;

export const apiEOD = {
    api_key: API_KEY_EOD,
    api: axios.create({
        baseURL: API_URL_EOD,
    }),

    async populateTickers(exchange: string, type: string = "stock", fmt: string = "json") {
        const url = `/exchange-symbol-list/${exchange}?api_token=${this.api_key}&type=${type}&fmt=${fmt}`;
        const response = await this.api.get(url);
        if (response?.status && response?.status >= 200 && response?.status < 300) {
            const statisticsOfAPIController = new StatisticsOfAPIController();
            statisticsOfAPIController.createStatisticsOfAPI(new Date(), LatestApi.eodhd, 1, url, EndpointType.symbol_list);
            return response;
        }
        consoleLog(`Error fetching tickers for ${exchange}: ${response?.status} - ${response?.statusText}`, "populateTickers", "ERROR");
        addLogJobExecution(LogLevel.ERROR, this.populateTickers.name, "Error fetching tickers", {error: response?.statusText}, 0);
        return response;
    },

    async getFundamentaData(stock: string, params: string = "") {
        const url = `/fundamentals/${stock}?api_token=${this.api_key}${params !== "" ? "&" + params : ""}`;
        const response = await this.api.get(url);
        if (response?.status && response?.status >= 200 && response?.status < 300) {
            const statisticsOfAPIController = new StatisticsOfAPIController();
            statisticsOfAPIController.createStatisticsOfAPI(new Date(), LatestApi.eodhd, 1, url, EndpointType.fundamentals);
            return response;
        }
        consoleLog(`Error fetching fundamental data for ${stock}: ${response?.status} - ${response?.statusText}`, "getFundamentaData", "ERROR");
        addLogJobExecution(LogLevel.ERROR, this.getFundamentaData.name, "Error fetching fundamental data", {error: response?.statusText}, 0);
        return response;
    },

    async getDividends(stock: string) {
        const url = `/div/${stock}?api_token=${this.api_key}&fmt=json`;
        const response = await this.api.get(url);
        if (response?.status && response?.status >= 200 && response?.status < 300) {
            const statisticsOfAPIController = new StatisticsOfAPIController();
            statisticsOfAPIController.createStatisticsOfAPI(new Date(), LatestApi.eodhd, 1, url, EndpointType.dividends);
            return response;
        }
        consoleLog(`Error fetching dividends for ${stock}: ${response?.status} - ${response?.statusText}`, "getDividends", "ERROR");
        addLogJobExecution(LogLevel.ERROR, this.getDividends.name, "Error fetching dividends", {error: response?.statusText}, 0);
        return response;
    },

    async getSplits(stock: string): Promise<any> {
        const url = `/splits/${stock}?api_token=${this.api_key}&fmt=json`;
        const response = await this.api.get(url);
        if (response?.status && response?.status >= 200 && response?.status < 300) {
            const statisticsOfAPIController = new StatisticsOfAPIController();
            statisticsOfAPIController.createStatisticsOfAPI(new Date(), LatestApi.eodhd, 1, url, EndpointType.splits);
            return response;
        }
        consoleLog(`Error fetching splits for ${stock}: ${response?.status} - ${response?.statusText}`, "getSplits", "ERROR");
        addLogJobExecution(LogLevel.ERROR, this.getSplits.name, "Error fetching splits", {error: response?.statusText}, 0);
        return response;
    },

    async getPrice(stock: string, others?: string[]): Promise<any> {
        let url = `/real-time/${stock}?`;
        if (others) {
            url += `s=${others?.toString()}&`;
        }
        url += `api_token=${this.api_key}&fmt=json`;
        const response = await this.api.get(url);
        if (response?.status && response?.status >= 200 && response?.status < 300) {
            const statisticsOfAPIController = new StatisticsOfAPIController();
            statisticsOfAPIController.createStatisticsOfAPI(new Date(), LatestApi.eodhd, 1, url, EndpointType.price);
            return response;
        }
        consoleLog(`Error fetching price for ${stock}: ${response?.status} - ${response?.statusText}`, "getPrice", "ERROR");
        addLogJobExecution(LogLevel.ERROR, this.getPrice.name, "Error fetching price", {error: response?.statusText}, 0);
        return response;
    },

    async getPriceHistory(stock: string, period: string = "d", from?: string, to?: string) {
        let url = "";
        if (!from && !to) {
            url = `/eod/${stock}?api_token=${this.api_key}&fmt=json&period=${period}`;
        }
        if (!to) {
            to = new Date().toISOString().split("T")[0];
        }
        url = `/eod/${stock}?api_token=${this.api_key}&fmt=json&from=${from}&to=${to}&period=${period}`;
        const response = await this.api.get(url);
        if (response?.status && response?.status >= 200 && response?.status < 300) {
            const statisticsOfAPIController = new StatisticsOfAPIController();
            statisticsOfAPIController.createStatisticsOfAPI(new Date(), LatestApi.eodhd, 1, url, EndpointType.price);
            return response;
        }
        consoleLog(`Error fetching price history for ${stock}: ${response?.status} - ${response?.statusText}`, "getPriceHistory", "ERROR");
        addLogJobExecution(LogLevel.ERROR, this.getPriceHistory.name, "Error fetching price history", {error: response?.statusText}, 0);
        return response;
    },
};
