//https://github.com/matthewmueller/date#examples
//https://date.js.org/

import {getAgenda, connectAgendaToMongoDB} from "./agenda";
import {
    _getDividendsSaveToDatabase,
    _getFundamentalDataAndSaveToS3,
    _getPricesSaveToDatabase,
    _getSplitsSaveToDatabase,
    _getTickersStatisticsAndCalculate,
    _populateListOfTickers,
    _processS3FilesGeneralDataToDatabase,
    _processS3FilesFinancialsToDatabase,
    _processEPSSaveToDatabase,
} from "../index";
import {ObjectId} from "mongodb";
import axios from "axios";

// Import the API URL from environment variables
const API_URL = process.env.API_URL || "http://localhost:3000";

// Interface for job history records
interface JobHistoryRecord {
    _id?: ObjectId;
    jobId: string;
    name: string;
    startedAt: Date;
    finishedAt?: Date;
    duration?: number; // in milliseconds
    status: "started" | "completed" | "failed";
    error?: string;
    result?: any;
    totalTickers?: number; // Total number of tickers to process
    processedTickers?: number; // Number of tickers processed so far
    progress?: number; // Progress percentage (0-100)
    lastProgressUpdate?: Date; // Last time progress was updated
}

// Get the job history collection
const getJobHistoryCollection = () => {
    const agenda = getAgenda();
    const db = agenda._mdb;
    return db.collection("jobHistory");
};

// Record job start in history
export const recordJobStart = async (job: any): Promise<ObjectId> => {
    try {
        const historyCollection = getJobHistoryCollection();

        const historyRecord: JobHistoryRecord = {
            jobId: job.attrs._id.toString(),
            name: job.attrs.name,
            startedAt: new Date(),
            status: "started",
        };

        const result = await historyCollection.insertOne(historyRecord);
        return result.insertedId;
    } catch (error) {
        console.error("Error recording job start:", error);
        throw error;
    }
};

// Record job completion in history
export const recordJobCompletion = async (historyId: ObjectId, job: any, error?: Error): Promise<void> => {
    try {
        const historyCollection = getJobHistoryCollection();
        const finishedAt = new Date();

        // Find the history record to get the startedAt time
        const historyRecord = await historyCollection.findOne({_id: historyId});

        if (!historyRecord) {
            console.error(`No job history record found with ID: ${historyId}`);
            return;
        }

        // Calculate duration in milliseconds
        const duration = finishedAt.getTime() - new Date(historyRecord.startedAt).getTime();

        // Update the history record
        await historyCollection.updateOne(
            {_id: historyId},
            {
                $set: {
                    finishedAt,
                    duration,
                    status: error ? "failed" : "completed",
                    error: error ? error.message : undefined,
                    progress: error ? historyRecord.progress : 100, // Set to 100% if completed successfully
                },
            },
        );
    } catch (updateError) {
        console.error("Error recording job completion:", updateError);
    }
};

// Initialize job progress tracking
export const initializeJobProgress = async (historyId: ObjectId, totalTickers: number): Promise<void> => {
    try {
        const historyCollection = getJobHistoryCollection();

        await historyCollection.updateOne(
            {_id: historyId},
            {
                $set: {
                    totalTickers,
                    processedTickers: 0,
                    progress: 0,
                    lastProgressUpdate: new Date(),
                },
            },
        );
    } catch (error) {
        console.error("Error initializing job progress:", error);
    }
};

// Update job progress
export const updateJobProgress = async (historyId: ObjectId, processedTickers: number): Promise<void> => {
    try {
        const historyCollection = getJobHistoryCollection();

        // Get current record to calculate progress
        const historyRecord = await historyCollection.findOne({_id: historyId});

        if (!historyRecord || !historyRecord.totalTickers) {
            console.error(`No job history record found with ID: ${historyId} or totalTickers not set`);
            return;
        }

        const progress = Math.round((processedTickers / historyRecord.totalTickers) * 100);

        await historyCollection.updateOne(
            {_id: historyId},
            {
                $set: {
                    processedTickers,
                    progress,
                    lastProgressUpdate: new Date(),
                },
            },
        );
    } catch (error) {
        console.error("Error updating job progress:", error);
    }
};

// Ensure job exists in database
export const ensureJobExists = async (
    name: string,
    data: any = {},
    options: {
        repeatInterval?: string | number;
        disabled?: boolean;
        executionOrder?: number;
    } = {},
): Promise<string> => {
    try {
        await connectAgendaToMongoDB();
        const agenda = getAgenda();

        // Check if job already exists
        const existingJobs = await agenda.jobs({name});

        if (existingJobs.length === 0) {
            // Create a new job
            let job;

            if (options.repeatInterval) {
                // Create a repeating job
                job = agenda.create(name, data);
                job.repeatEvery(options.repeatInterval.toString());
                //@ts-ignore
                job.attrs.executionOrder = options.executionOrder;

                if (options.disabled) {
                    job.disable();
                }

                await job.save();
            } else {
                // Create a one-time job that doesn't run immediately
                job = agenda.create(name, data);
                //@ts-ignore
                job.attrs.executionOrder = options.executionOrder;
                if (options.disabled) {
                    job.disable();
                }

                await job.save();
            }

            return job.attrs._id.toString();
        } else {
            // Job already exists, return its ID
            return existingJobs[0].attrs._id.toString();
        }
    } catch (error) {
        console.error(`Error ensuring job ${name} exists:`, error);
        throw error;
    }
};

// Helper function to define a job with standard behavior
const defineStandardJob = (
    agenda: any,
    jobName: string,
    jobHandler: (job: any) => Promise<void>,
    options: {
        repeatInterval?: string;
        disabled?: boolean;
        executionOrder?: number;
    } = {},
) => {
    // Define the job with our standard wrapper
    agenda.define(jobName, async (job: any) => {
        // Get the job name for generic handling
        const jobName = job.attrs.name;

        try {
            // Get the job history collection to check for recent runs
            const historyCollection = getJobHistoryCollection();

            // Configuration: how far back to check for recent runs (in minutes)
            const RECENT_JOB_WINDOW_MINUTES = 10;

            // Check if this job has run recently
            const recentTimeWindow = new Date(Date.now() - RECENT_JOB_WINDOW_MINUTES * 60 * 1000);
            const recentRuns = await historyCollection
                .find({
                    name: jobName,
                    startedAt: {$gt: recentTimeWindow},
                })
                .toArray();
            //console.log("recentTimeWindow", recentTimeWindow, recentRuns);
            // If there are recent runs and this isn't a forced run, skip it
            if (recentRuns.length > 1 && !job.attrs.data?.force) {
                console.log(`Skipping ${jobName} job as it was already run ${recentRuns.length} times in the last ${RECENT_JOB_WINDOW_MINUTES} minutes`);
                return;
            }

            // Check if this is a scheduled run or a manual run
            const isScheduledRun = job.attrs.nextRunAt && (job.attrs.repeatInterval || job.attrs.type === "scheduled");

            // Only proceed if this is a scheduled run or explicitly forced
            if (isScheduledRun || job.attrs.data?.force) {
                try {
                    console.log(`Starting ${jobName} job`, {
                        isScheduledRun,
                        jobType: job.attrs.type,
                        repeatInterval: job.attrs.repeatInterval,
                        nextRunAt: job.attrs.nextRunAt,
                        recentRuns: recentRuns.length,
                    });
                    // Execute the job handler
                    await jobHandler(job);
                    console.log(`${jobName} executed successfully`);
                } finally {
                    // Reset the force flag after execution if it was set
                    job.attrs.data.force = false;
                    job.attrs.data.tickers = undefined;
                    await job.save();
                    console.log(`Reset force flag for ${jobName} job`);
                }
            } else {
                console.log(`Skipping ${jobName} job as it was not triggered by schedule`);
                // We don't throw an error here as the job wasn't meant to run
            }
        } catch (error: any) {
            console.error(`Error executing ${jobName} job:`, error);
            throw error; // Rethrow to mark job as failed
        }
    });

    // Ensure the job exists in the database with the specified options
    return ensureJobExists(jobName, {}, options);
};

// Define all jobs with history tracking
export const defineJobs = async (): Promise<void> => {
    const agenda = getAgenda();

    // Set up global job completion tracking
    agenda.on("start", async (job) => {
        const historyId = await recordJobStart(job);
        // Store the history ID in the job for later reference
        job.attrs.historyId = historyId;
        await job.save();
    });

    agenda.on("complete", async (job) => {
        if (job.attrs.historyId) {
            await recordJobCompletion(job.attrs.historyId, job);
        }
    });

    agenda.on("fail", async (error, job) => {
        if (job.attrs.historyId) {
            await recordJobCompletion(job.attrs.historyId, job, error);
        }
    });

    // Define populate-stock-tickers job using the standard job handler
    await defineStandardJob(
        agenda,
        "populate-stock-tickers",
        async () => {
            await _populateListOfTickers();
        },
        {
            // Use a more reliable cron-style format: "0 0 * * 6" means "At 00:00 on every Saturday"
            repeatInterval: "0 0 * * 6",
            disabled: false,
            executionOrder: 1,
        },
    );

    // Define get-fundamental-data-and-save-to-S3 job using the standard job handler
    await defineStandardJob(
        agenda,
        "get-fundamental-data-and-save-to-S3",
        async (job) => {
            console.log("get-fundamental-data-and-save-to-S3 job", job);
            const tickerCodes: string[] = job.attrs.data.tickers;
            const historyId = job.attrs.historyId?.toString();
            console.log("get-fundamental-data-and-save-to-S3 tickerCodes", tickerCodes);
            if (tickerCodes && tickerCodes.length > 0) {
                await _getFundamentalDataAndSaveToS3(tickerCodes, historyId);
            } else {
                await _getFundamentalDataAndSaveToS3(undefined, historyId);
            }
            // Only schedule the next job if this was a scheduled run
            if (!job.attrs.data?.force) {
                try {
                    console.log("Scheduling process-s3-files-general-data-to-add-to-database job to run after get-fundamental-data-and-save-to-S3");
                    // Use the internal agenda API to schedule the next job
                    const response = await axios.get(`${API_URL}/api/jobs/details?name=process-s3-files-general-data-to-add-to-database`);
                    const jobId = response.data.job._id;
                    await axios.post(`${API_URL}/api/jobs/run`, {
                        id: jobId,
                    });
                    console.log("Successfully scheduled process-s3-files-general-data-to-add-to-database job");
                } catch (error) {
                    console.error("Failed to schedule process-s3-files-general-data-to-add-to-database job:", error);
                }
            } else {
                console.log("Skipping scheduling of process-s3-files-general-data-to-add-to-database job as this was a manual run");
            }
        },
        {
            // Use a more reliable cron-style format: "9 0 * * 6" means "Every Saturday at 00:09"
            repeatInterval: "9 0 * * 6",
            disabled: false,
            executionOrder: 2,
        },
    );

    // Define process-s3-files-general-data-to-add-to-database job using the standard job handler
    await defineStandardJob(
        agenda,
        "process-s3-files-general-data-to-add-to-database",
        async (job) => {
            console.log("process-s3-files-general-data-to-add-to-database job", job);
            const tickerCodes: string[] = job.attrs.data.tickers;
            const historyId = job.attrs.historyId?.toString();
            console.log("process-s3-files-general-data-to-add-to-database tickerCodes", tickerCodes);
            if (tickerCodes && tickerCodes.length > 0) {
                await _processS3FilesGeneralDataToDatabase(tickerCodes, historyId);
            } else {
                await _processS3FilesGeneralDataToDatabase(undefined, historyId);
            }
            // Only schedule the next job if this was a scheduled run
            if (!job.attrs.data?.force) {
                try {
                    console.log("Scheduling process-s3-files-to-add-to-database job to run after process-s3-files-general-data-to-add-to-database");
                    // Use the internal agenda API to schedule the next job
                    const response = await axios.get(`${API_URL}/api/jobs/details?name=process-s3-files-to-add-to-database`);
                    const jobId = response.data.job._id;
                    await axios.post(`${API_URL}/api/jobs/run`, {
                        id: jobId,
                    });
                    console.log("Successfully scheduled process-s3-files-to-add-to-database job");
                } catch (error) {
                    console.error("Failed to schedule process-s3-files-to-add-to-database job:", error);
                }
            } else {
                console.log("Skipping scheduling of process-s3-files-to-add-to-database job as this was a manual run");
            }
        },
        {
            disabled: false,
            executionOrder: 3,
        },
    );

    // Define process-s3-files-to-add-to-database job using the standard job handler
    await defineStandardJob(
        agenda,
        "process-s3-files-to-add-to-database",
        async (job) => {
            console.log("process-s3-files-to-add-to-database job", job);
            const tickerCodes: string[] = job.attrs.data.tickers;
            const historyId = job.attrs.historyId?.toString();
            console.log("process-s3-files-to-add-to-database tickerCodes", tickerCodes);
            if (tickerCodes && tickerCodes.length > 0) {
                await _processS3FilesFinancialsToDatabase(tickerCodes, historyId);
            } else {
                await _processS3FilesFinancialsToDatabase(undefined, historyId);
            }
            // Only schedule the next job if this was a scheduled run
            if (!job.attrs.data?.force) {
                try {
                    console.log("Scheduling get-dividends job to run after process-s3-files-to-add-to-database");
                    // Use the internal agenda API to schedule the next job
                    const response = await axios.get(`${API_URL}/api/jobs/details?name=get-dividends`);
                    const jobId = response.data.job._id;
                    await axios.post(`${API_URL}/api/jobs/run`, {
                        id: jobId,
                    });
                    console.log("Successfully scheduled get-dividends job");
                } catch (error) {
                    console.error("Failed to schedule get-dividends job:", error);
                }
            } else {
                console.log("Skipping scheduling of get-dividends job as this was a manual run");
            }
        },
        {
            disabled: false,
            executionOrder: 4,
        },
    );

    await defineStandardJob(
        agenda,
        "get-dividends",
        async (job) => {
            console.log("get-dividends job", job);
            const tickerCodes: string[] = job.attrs.data.tickers;
            const historyId = job.attrs.historyId?.toString();
            console.log("get-dividends tickerCodes", tickerCodes);
            if (tickerCodes && tickerCodes.length > 0) {
                await _getDividendsSaveToDatabase(tickerCodes, historyId);
            } else {
                await _getDividendsSaveToDatabase(undefined, historyId);
            }
            // Only schedule the next job if this was a scheduled run
            if (!job.attrs.data?.force) {
                try {
                    console.log("Scheduling set-eps job to run after get-dividends");
                    // Use the internal agenda API to schedule the next job
                    const response = await axios.get(`${API_URL}/api/jobs/details?name=set-eps`);
                    const jobId = response.data.job._id;
                    await axios.post(`${API_URL}/api/jobs/run`, {
                        id: jobId,
                    });
                    console.log("Successfully scheduled set-eps job");
                } catch (error) {
                    console.error("Failed to schedule set-eps job:", error);
                }
            } else {
                console.log("Skipping scheduling of set-eps job as this was a manual run");
            }
        },
        {
            disabled: false,
            executionOrder: 5,
        },
    );

    await defineStandardJob(
        agenda,
        "set-eps",
        async (job) => {
            console.log("set-eps job", job);
            const tickerCodes: string[] = job.attrs.data.tickers;
            const historyId = job.attrs.historyId?.toString();
            console.log("set-eps tickerCodes", tickerCodes);
            if (tickerCodes && tickerCodes.length > 0) {
                await _processEPSSaveToDatabase(tickerCodes, historyId);
            } else {
                await _processEPSSaveToDatabase(undefined, historyId);
            }
        },
        {
            disabled: false,
            executionOrder: 6,
        },
    );

    await defineStandardJob(
        agenda,
        "get-splits",
        async (job) => {
            console.log("get-splits job", job);
            const tickerCodes: string[] = job.attrs.data.tickers;
            const historyId = job.attrs.historyId?.toString();
            console.log("get-splits tickerCodes", tickerCodes);
            if (tickerCodes && tickerCodes.length > 0) {
                await _getSplitsSaveToDatabase(tickerCodes, historyId);
            } else {
                await _getSplitsSaveToDatabase(undefined, historyId);
            }
        },
        {
            // Use a more reliable cron-style format: "0 0 * * 0" means "At 00:00 on every Sunday"
            repeatInterval: "0 0 * * 0",
            disabled: false,
            executionOrder: 7,
        },
    );

    await defineStandardJob(
        agenda,
        "get-prices",
        async (job) => {
            console.log("get-prices job", job);
            const tickerCodes: string[] = job.attrs.data.tickers;
            const historyId = job.attrs.historyId?.toString();
            console.log("get-prices tickerCodes", tickerCodes);
            if (tickerCodes && tickerCodes.length > 0) {
                await _getPricesSaveToDatabase(tickerCodes, historyId);
            } else {
                await _getPricesSaveToDatabase(undefined, historyId);
            }
            // Only schedule the next job if this was a scheduled run
            if (!job.attrs.data?.force) {
                try {
                    console.log("Scheduling get-statistics job to run after get-prices");
                    // Use the internal agenda API to schedule the next job
                    const response = await axios.get(`${API_URL}/api/jobs/details?name=get-statistics`);
                    const jobId = response.data.job._id;
                    await axios.post(`${API_URL}/api/jobs/run`, {
                        id: jobId,
                    });
                    console.log("Successfully scheduled get-statistics job");
                } catch (error) {
                    console.error("Failed to schedule get-statistics job:", error);
                }
            } else {
                console.log("Skipping scheduling of get-statistics job as this was a manual run");
            }
        },
        {
            repeatInterval: "0 7-17 * * 1-5", // This single cron job will run at the top of every hour between 7:00 UTC and 17:00 UTC. From Monday to Friday. Ideal should be 7-20
            disabled: false,
            executionOrder: 8,
        },
    );

    await defineStandardJob(
        agenda,
        "get-statistics",
        async (job) => {
            console.log("get-statistics job", job);
            const tickerCodes: string[] = job.attrs.data.tickers;
            const historyId = job.attrs.historyId?.toString();
            console.log("get-statistics tickerCodes", tickerCodes);
            if (tickerCodes && tickerCodes.length > 0) {
                await _getTickersStatisticsAndCalculate(tickerCodes, historyId);
            } else {
                await _getTickersStatisticsAndCalculate(undefined, historyId);
            }
        },
        {
            disabled: false,
            executionOrder: 9,
        },
    );

    // You can define more jobs here following the same pattern
    // Example:
    // await defineStandardJob(
    //     agenda,
    //     "another-job-name",
    //     async () => {
    //         await anotherJobFunction();
    //     },
    //     {
    //         repeatInterval: "0 0 * * *", // Daily at midnight
    //         disabled: false,
    //     }
    // );

    // Add more job definitions here as needed
};

// Clean up duplicate jobs
export const cleanupDuplicateJobs = async (): Promise<void> => {
    try {
        await connectAgendaToMongoDB();
        const agenda = getAgenda();

        // Get all jobs grouped by name
        const db = agenda._mdb;
        const jobNames = await db.collection("agendaJobs").distinct("name");

        for (const jobName of jobNames) {
            // Get all jobs with this name
            const jobs = await agenda.jobs({name: jobName});

            if (jobs.length > 1) {
                console.log(`Found ${jobs.length} instances of job "${jobName}". Keeping only the most recent one.`);

                // Sort jobs by creation time (newest first)
                jobs.sort((a, b) => {
                    // Get timestamps for comparison, defaulting to 0 if not available
                    const aTime = a.attrs.lastRunAt ? new Date(a.attrs.lastRunAt).getTime() : 0;
                    const bTime = b.attrs.lastRunAt ? new Date(b.attrs.lastRunAt).getTime() : 0;

                    return bTime - aTime; // Sort descending (newest first)
                });

                // Keep the first job (most recent) and remove the rest
                const jobToKeep = jobs[0];
                for (let i = 1; i < jobs.length; i++) {
                    await agenda.cancel({_id: jobs[i].attrs._id});
                }

                console.log(`Kept job ${jobToKeep.attrs._id} and removed ${jobs.length - 1} duplicate(s) for "${jobName}"`);
            }
        }
    } catch (error) {
        console.error("Error cleaning up duplicate jobs:", error);
    }
};

// Initialize all job definitions
export const initializeJobs = async (): Promise<void> => {
    try {
        await connectAgendaToMongoDB();

        // Clean up any duplicate jobs first
        await cleanupDuplicateJobs();

        // Define jobs
        await defineJobs();

        console.log("Job definitions initialized successfully");
    } catch (error) {
        console.error("Error initializing job definitions:", error);
        throw error;
    }
};

export default {
    initializeJobs,
    ensureJobExists,
    defineJobs,
    cleanupDuplicateJobs,
};
