import type {NextApiRequest, NextApiResponse} from "next";
import {getAgenda} from "@/lib/agenda";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {ObjectId} from "mongodb";

interface LogEntry {
    _id: string;
    timestamp: string;
    logLevel: "ERROR" | "WARNING" | "INFO" | "DEBUG";
    functionName: string;
    message: string;
    data: any;
    ticker_internal_id?: number;
}

interface LogsResponse {
    logs: LogEntry[];
    totalCount: number;
    page: number;
    pageSize: number;
    totalPages: number;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<LogsResponse | {error: string; details?: string}>) {
    if (req.method !== "GET") {
        return res.status(405).json({error: "Method not allowed"});
    }

    try {
        // Ensure MongoDB connection is established
        await checkIfAgendaIsConnected();

        // Parse query parameters
        const page = parseInt((req.query.page as string) || "1", 10);
        const pageSize = parseInt((req.query.pageSize as string) || "50", 10);
        const sortBy = (req.query.sortBy as string) || "timestamp";
        const sortDirection = (req.query.sortDirection as string) || "desc";

        // Parse filter parameters
        const filterId = req.query.filterId as string;
        const filterLogLevel = req.query.filterLogLevel as string;
        const filterFunctionName = req.query.filterFunctionName as string;
        const filterMessage = req.query.filterMessage as string;
        const filterTickerInternalId = req.query.filterTickerInternalId as string;

        // Parse timestamp filter parameters
        const timestampFilter = req.query.timestampFilter as string;
        const timestampValue = req.query.timestampValue as string;
        const timestampFrom = req.query.timestampFrom as string;
        const timestampTo = req.query.timestampTo as string;

        // Get MongoDB collection
        const agenda = getAgenda();
        const db = agenda._mdb;
        const collection = db.collection("jobLogs");

        // Build MongoDB query
        const query: any = {};

        // Apply filters
        if (filterId) {
            try {
                query._id = new ObjectId(filterId);
            } catch {
                // If not a valid ObjectId, search in string representation
                query._id = {$regex: filterId, $options: "i"};
            }
        }

        if (filterLogLevel) {
            query.logLevel = filterLogLevel;
        }

        if (filterFunctionName) {
            query.functionName = {$regex: filterFunctionName, $options: "i"};
        }

        if (filterMessage) {
            query.message = {$regex: filterMessage, $options: "i"};
        }

        if (filterTickerInternalId) {
            const tickerId = parseInt(filterTickerInternalId, 10);
            if (!isNaN(tickerId)) {
                query.ticker_internal_id = tickerId;
            }
        }

        // Apply timestamp filters
        if (timestampFilter && timestampFilter !== "none") {
            if (timestampFilter === "exact") {
                if (timestampValue) {
                    const date = new Date(timestampValue);
                    if (!isNaN(date.getTime())) {
                        // Check if the input was just a date (no specific time)
                        // This handles cases where user inputs just a date or date with 00:00:00
                        const timeComponent = timestampValue.split("T")[1] || "";
                        const isJustDate = !timeComponent || timeComponent === "00:00:00.000Z" || timeComponent === "00:00:00";

                        if (isJustDate) {
                            // If no specific time, match the entire day
                            const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);
                            const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999);

                            query.timestamp = {
                                $gte: startOfDay,
                                $lte: endOfDay,
                            };
                        } else {
                            // If time specified, match the exact second (0-999 milliseconds)
                            const startOfSecond = new Date(date);
                            startOfSecond.setMilliseconds(0);
                            const endOfSecond = new Date(date);
                            endOfSecond.setMilliseconds(999);

                            query.timestamp = {
                                $gte: startOfSecond,
                                $lte: endOfSecond,
                            };
                        }
                    }
                }
            } else if (timestampFilter === "before") {
                if (timestampValue) {
                    const date = new Date(timestampValue);
                    if (!isNaN(date.getTime())) {
                        query.timestamp = {$lt: date};
                    }
                }
            } else if (timestampFilter === "after") {
                if (timestampValue) {
                    const date = new Date(timestampValue);
                    if (!isNaN(date.getTime())) {
                        query.timestamp = {$gt: date};
                    }
                }
            } else if (timestampFilter === "between") {
                const timestampQuery: any = {};

                if (timestampFrom) {
                    const fromDate = new Date(timestampFrom);
                    if (!isNaN(fromDate.getTime())) {
                        timestampQuery.$gte = fromDate;
                    }
                }

                if (timestampTo) {
                    const toDate = new Date(timestampTo);
                    if (!isNaN(toDate.getTime())) {
                        timestampQuery.$lte = toDate;
                    }
                }

                if (Object.keys(timestampQuery).length > 0) {
                    query.timestamp = timestampQuery;
                }
            }
        }

        // Build sort object
        const sortObject: any = {};
        if (sortBy === "timestamp") {
            sortObject.timestamp = sortDirection === "asc" ? 1 : -1;
        } else if (sortBy === "_id") {
            sortObject._id = sortDirection === "asc" ? 1 : -1;
        } else if (sortBy === "logLevel") {
            sortObject.logLevel = sortDirection === "asc" ? 1 : -1;
        } else if (sortBy === "functionName") {
            sortObject.functionName = sortDirection === "asc" ? 1 : -1;
        } else if (sortBy === "message") {
            sortObject.message = sortDirection === "asc" ? 1 : -1;
        } else if (sortBy === "ticker_internal_id") {
            sortObject.ticker_internal_id = sortDirection === "asc" ? 1 : -1;
        } else {
            // Default sort by timestamp descending
            sortObject.timestamp = -1;
        }

        // Get total count for pagination
        const totalCount = await collection.countDocuments(query);

        // Calculate pagination
        const skip = (page - 1) * pageSize;

        // Fetch logs with pagination and sorting
        const logs = await collection.find(query).sort(sortObject).skip(skip).limit(pageSize).toArray();

        // Transform logs to match the expected format
        const transformedLogs: LogEntry[] = logs.map((log) => ({
            _id: log._id.toString(),
            timestamp: log.timestamp.toISOString(),
            logLevel: log.logLevel,
            functionName: log.functionName,
            message: log.message,
            data: log.data || {},
            ticker_internal_id: log.ticker_internal_id,
        }));

        return res.status(200).json({
            logs: transformedLogs,
            totalCount,
            page,
            pageSize,
            totalPages: Math.ceil(totalCount / pageSize),
        });
    } catch (error) {
        console.error("Error fetching logs:", error);
        return res.status(500).json({
            error: "Failed to fetch logs",
            details: error instanceof Error ? error.message : "Unknown error",
        });
    }
}
