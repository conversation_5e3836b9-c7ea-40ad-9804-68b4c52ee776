import {NextApiRequest, NextApiResponse} from "next";
import {getOrCreateAgendaSettings, updateAgendaSettings} from "@/lib/agenda";
import {checkIfAgendaIsConnected} from "@/lib/utils";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    // Ensure connection to MongoDB
    try {
        await checkIfAgendaIsConnected();
    } catch (error) {
        return res.status(500).json({error: "Failed to connect to database"});
    }

    // Handle GET request
    if (req.method === "GET") {
        try {
            const settings = await getOrCreateAgendaSettings();
            return res.status(200).json(settings);
        } catch (error) {
            console.error("Error fetching agenda settings:", error);
            return res.status(500).json({error: "Failed to fetch agenda settings"});
        }
    }

    // Handle PUT request
    if (req.method === "PUT") {
        try {
            const {checkAgendaStatusInterval, priceBatchSize, priceChunkSize, dividendsBatchSize, fundamentalDataBatchSize, processS3FilesBatchSize, splitsBatchSize, epsBatchSize, logLevel} =
                req.body;
            const updateData: any = {};

            // Validate checkAgendaStatusInterval
            if (checkAgendaStatusInterval !== undefined) {
                const interval = Number(checkAgendaStatusInterval);
                if (isNaN(interval) || interval <= 0) {
                    return res.status(400).json({error: "checkAgendaStatusInterval must be a positive number"});
                }
                updateData.checkAgendaStatusInterval = interval;
            }

            // Validate priceBatchSize
            if (priceBatchSize !== undefined) {
                const batchSize = Number(priceBatchSize);
                if (isNaN(batchSize) || batchSize <= 0 || batchSize > 100) {
                    return res.status(400).json({error: "priceBatchSize must be a positive number between 1 and 100"});
                }
                updateData.priceBatchSize = batchSize;
            }

            // Validate priceChunkSize
            if (priceChunkSize !== undefined) {
                const chunkSize = Number(priceChunkSize);
                if (isNaN(chunkSize) || chunkSize <= 0 || chunkSize > 5000) {
                    return res.status(400).json({error: "priceChunkSize must be a positive number between 1 and 5000"});
                }
                updateData.priceChunkSize = chunkSize;
            }

            // Validate dividendsBatchSize
            if (dividendsBatchSize !== undefined) {
                const batchSize = Number(dividendsBatchSize);
                if (isNaN(batchSize) || batchSize <= 0 || batchSize > 50) {
                    return res.status(400).json({error: "dividendsBatchSize must be a positive number between 1 and 50"});
                }
                updateData.dividendsBatchSize = batchSize;
            }

            // Validate fundamentalDataBatchSize
            if (fundamentalDataBatchSize !== undefined) {
                const batchSize = Number(fundamentalDataBatchSize);
                if (isNaN(batchSize) || batchSize <= 0 || batchSize > 50) {
                    return res.status(400).json({error: "fundamentalDataBatchSize must be a positive number between 1 and 50"});
                }
                updateData.fundamentalDataBatchSize = batchSize;
            }

            // Validate processS3FilesBatchSize
            if (processS3FilesBatchSize !== undefined) {
                const batchSize = Number(processS3FilesBatchSize);
                if (isNaN(batchSize) || batchSize <= 0 || batchSize > 50) {
                    return res.status(400).json({error: "processS3FilesBatchSize must be a positive number between 1 and 50"});
                }
                updateData.processS3FilesBatchSize = batchSize;
            }

            // Validate splitsBatchSize
            if (splitsBatchSize !== undefined) {
                const batchSize = Number(splitsBatchSize);
                if (isNaN(batchSize) || batchSize <= 0 || batchSize > 50) {
                    return res.status(400).json({error: "splitsBatchSize must be a positive number between 1 and 50"});
                }
                updateData.splitsBatchSize = batchSize;
            }

            // Validate epsBatchSize
            if (epsBatchSize !== undefined) {
                const batchSize = Number(epsBatchSize);
                if (isNaN(batchSize) || batchSize <= 0 || batchSize > 50) {
                    return res.status(400).json({error: "epsBatchSize must be a positive number between 1 and 50"});
                }
                updateData.epsBatchSize = batchSize;
            }

            // Validate logLevel
            if (logLevel !== undefined) {
                const validLogLevels = ["ERROR", "WARNING", "INFO", "DEBUG"];
                if (!validLogLevels.includes(logLevel)) {
                    return res.status(400).json({error: "Invalid logLevel. Must be one of: " + validLogLevels.join(", ")});
                }
                updateData.logLevel = logLevel;
            }

            // Check if any valid settings were provided
            if (Object.keys(updateData).length === 0) {
                return res.status(400).json({error: "No valid settings provided"});
            }

            const updatedSettings = await updateAgendaSettings(updateData);
            return res.status(200).json(updatedSettings);
        } catch (error) {
            console.error("Error updating agenda settings:", error);
            return res.status(500).json({error: "Failed to update agenda settings"});
        }
    }

    // Handle unsupported methods
    return res.status(405).json({error: "Method not allowed"});
}
