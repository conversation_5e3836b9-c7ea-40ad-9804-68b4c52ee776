"use client";

import type React from "react";

import {useState, useEffect} from "react";
import {ChevronLeft, ChevronRight, Filter, X} from "lucide-react";
import {Button} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {Badge} from "@/components/ui/badge";
import styles from "./page.module.css";

const getLogLevelBadgeClass = (level: string) => {
    switch (level) {
        case "ERROR":
            return styles.errorBadge;
        case "WARNING":
            return styles.warningBadge;
        case "INFO":
            return styles.infoBadge;
        case "DEBUG":
            return styles.debugBadge;
        default:
            return styles.defaultBadge;
    }
};

const SortableHeader = ({column, children, onSort, sortConfig}: {column: keyof LogEntry; children: React.ReactNode; onSort: (c: keyof LogEntry) => void; sortConfig: SortConfig}) => (
    <th className={styles.tableHead}>
        <button className={styles.sortButton} onClick={() => onSort(column)}>
            {children}
            <span className={styles.sortIcon}>{sortConfig.key === column ? (sortConfig.direction === "asc" ? "↑" : "↓") : "↕"}</span>
        </button>
    </th>
);

interface LogEntry {
    _id: string;
    timestamp: string;
    logLevel: "ERROR" | "WARNING" | "INFO" | "DEBUG";
    functionName: string;
    message: string;
    data: any;
    ticker_internal_id?: number;
}

interface SortConfig {
    key: keyof LogEntry;
    direction: "asc" | "desc";
}

interface FilterConfig {
    _id: string;
    logLevel: string;
    functionName: string;
    message: string;
    ticker_internal_id: string;
    timestampFilter: "none" | "exact" | "before" | "after" | "between";
    timestampValue: string; // For exact, before, after
    timestampFrom: string; // For between range
    timestampTo: string; // For between range
}

// API function to fetch logs from MongoDB
const fetchLogs = async (page: number, pageSize: number, sortConfig: SortConfig, filters: FilterConfig): Promise<{logs: LogEntry[]; totalCount: number}> => {
    try {
        const queryParams = new URLSearchParams();
        queryParams.append("page", page.toString());
        queryParams.append("pageSize", pageSize.toString());
        queryParams.append("sortBy", sortConfig.key);
        queryParams.append("sortDirection", sortConfig.direction);

        // Add filters to query params
        if (filters._id) queryParams.append("filterId", filters._id);
        if (filters.logLevel && filters.logLevel !== "ALL") queryParams.append("filterLogLevel", filters.logLevel);
        if (filters.functionName) queryParams.append("filterFunctionName", filters.functionName);
        if (filters.message) queryParams.append("filterMessage", filters.message);
        if (filters.ticker_internal_id) queryParams.append("filterTickerInternalId", filters.ticker_internal_id);

        // Add timestamp filters
        if (filters.timestampFilter !== "none") {
            queryParams.append("timestampFilter", filters.timestampFilter);
            if (filters.timestampFilter === "exact" || filters.timestampFilter === "before" || filters.timestampFilter === "after") {
                if (filters.timestampValue) queryParams.append("timestampValue", filters.timestampValue);
            } else if (filters.timestampFilter === "between") {
                if (filters.timestampFrom) queryParams.append("timestampFrom", filters.timestampFrom);
                if (filters.timestampTo) queryParams.append("timestampTo", filters.timestampTo);
            }
        }

        const response = await fetch(`/api/jobs/logs?${queryParams.toString()}`);

        if (!response.ok) {
            throw new Error(`Failed to fetch logs: ${response.statusText}`);
        }

        const data = await response.json();
        return {
            logs: data.logs,
            totalCount: data.totalCount,
        };
    } catch (error) {
        console.error("Error fetching logs:", error);
        throw error;
    }
};

export default function LogsPage() {
    const [logs, setLogs] = useState<LogEntry[]>([]);
    const [loading, setLoading] = useState(true);
    const [sortConfig, setSortConfig] = useState<SortConfig>({key: "timestamp", direction: "desc"});
    const [filters, setFilters] = useState<FilterConfig>({
        _id: "",
        logLevel: "ALL",
        functionName: "",
        message: "",
        ticker_internal_id: "",
        timestampFilter: "none",
        timestampValue: "",
        timestampFrom: "",
        timestampTo: "",
    });
    const [showFilters, setShowFilters] = useState(false);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(50);
    const [totalCount, setTotalCount] = useState(0);

    useEffect(() => {
        const loadLogs = async () => {
            setLoading(true);
            try {
                const data = await fetchLogs(page, pageSize, sortConfig, filters);
                setLogs(data.logs);
                setTotalCount(data.totalCount);
            } catch (error) {
                console.error("Failed to fetch logs:", error);
            } finally {
                setLoading(false);
            }
        };

        loadLogs();
    }, [page, pageSize, sortConfig, filters]);

    const handleSort = (key: keyof LogEntry) => {
        let direction: "asc" | "desc" = "asc";
        if (sortConfig.key === key && sortConfig.direction === "asc") {
            direction = "desc";
        }
        setSortConfig({key, direction});
        setPage(1); // Reset to first page when sorting
    };

    const handleFilterChange = (key: keyof FilterConfig, value: string) => {
        setFilters((prev) => ({...prev, [key]: value}));
        setPage(1); // Reset to first page when filtering
    };

    // Helper function to format date/time for input fields
    const formatDateTimeForInput = (dateStr: string): string => {
        if (!dateStr) return "";
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return "";

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");

        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    };

    // Helper function to parse date/time from input and convert to ISO string
    const parseDateTimeInput = (input: string): string => {
        if (!input.trim()) return "";

        // Handle different input formats
        let dateStr = input.trim();

        // If only date is provided (dd/MM/yyyy), add time for start/end of day
        if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
            // For exact date matching, we'll handle this in the API
            dateStr += " 00:00:00";
        }

        // Parse dd/MM/yyyy HH:mm:ss format
        const match = dateStr.match(/^(\d{2})\/(\d{2})\/(\d{4})\s+(\d{2}):(\d{2}):(\d{2})$/);
        if (match) {
            const [, day, month, year, hours, minutes, seconds] = match;
            const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), parseInt(hours), parseInt(minutes), parseInt(seconds));
            if (!isNaN(date.getTime())) {
                return date.toISOString();
            }
        }

        return "";
    };

    const clearFilters = () => {
        setFilters({
            _id: "",
            logLevel: "ALL",
            functionName: "",
            message: "",
            ticker_internal_id: "",
            timestampFilter: "none",
            timestampValue: "",
            timestampFrom: "",
            timestampTo: "",
        });
        setPage(1); // Reset to first page when clearing filters
    };

    if (loading) {
        return (
            <div className={styles.container}>
                <div className={styles.loadingContainer}>
                    <div className={styles.spinner}></div>
                    <p>Loading logs...</p>
                </div>
            </div>
        );
    }

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <div>
                    <h2 className={styles.title}>Logs</h2>
                    <p className={styles.description}>View and analyze system logs with filtering and sorting capabilities</p>
                </div>
                <div className={styles.headerActions}>
                    <Button variant="outline" onClick={() => setShowFilters(!showFilters)} className={styles.filterToggle}>
                        <Filter className={styles.filterIcon} />
                        {showFilters ? "Hide Filters" : "Show Filters"}
                    </Button>
                </div>
            </div>

            {showFilters && (
                <div className={styles.filtersCard}>
                    <div className={styles.filtersHeader}>
                        <h3 className={styles.filtersTitle}>Filters</h3>
                        <Button variant="ghost" size="sm" onClick={clearFilters}>
                            <X className={styles.clearIcon} />
                            Clear All
                        </Button>
                    </div>
                    <div className={styles.filtersGrid}>
                        <div className={styles.filterGroup}>
                            <Label htmlFor="filter-id">ID</Label>
                            <Input id="filter-id" placeholder="Filter by ID..." value={filters._id} onChange={(e) => handleFilterChange("_id", e.target.value)} />
                        </div>
                        <div className={styles.filterGroup}>
                            <Label htmlFor="filter-level">Log Level</Label>
                            <Select value={filters.logLevel} onValueChange={(value) => handleFilterChange("logLevel", value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All levels" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="ALL">All levels</SelectItem>
                                    <SelectItem value="ERROR">Error</SelectItem>
                                    <SelectItem value="WARNING">Warning</SelectItem>
                                    <SelectItem value="INFO">Info</SelectItem>
                                    <SelectItem value="DEBUG">Debug</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className={styles.filterGroup}>
                            <Label htmlFor="filter-function">Function Name</Label>
                            <Input id="filter-function" placeholder="Filter by function..." value={filters.functionName} onChange={(e) => handleFilterChange("functionName", e.target.value)} />
                        </div>
                        <div className={styles.filterGroup}>
                            <Label htmlFor="filter-message">Message</Label>
                            <Input id="filter-message" placeholder="Filter by message..." value={filters.message} onChange={(e) => handleFilterChange("message", e.target.value)} />
                        </div>
                        <div className={styles.filterGroup}>
                            <Label htmlFor="filter-ticker">Ticker ID</Label>
                            <Input
                                id="filter-ticker"
                                placeholder="Filter by ticker ID..."
                                value={filters.ticker_internal_id}
                                onChange={(e) => handleFilterChange("ticker_internal_id", e.target.value)}
                            />
                        </div>

                        {/* Timestamp Filter Section */}
                        <div className={styles.filterGroup} style={{gridColumn: "1 / -1"}}>
                            <Label htmlFor="filter-timestamp-type">Timestamp Filter</Label>
                            <Select value={filters.timestampFilter} onValueChange={(value) => handleFilterChange("timestampFilter", value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="No timestamp filter" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="none">No filter</SelectItem>
                                    <SelectItem value="exact">Exact date/time</SelectItem>
                                    <SelectItem value="before">Before date/time</SelectItem>
                                    <SelectItem value="after">After date/time</SelectItem>
                                    <SelectItem value="between">Between dates</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        {/* Single Date/Time Input for exact, before, after */}
                        {(filters.timestampFilter === "exact" || filters.timestampFilter === "before" || filters.timestampFilter === "after") && (
                            <div className={styles.filterGroup} style={{gridColumn: "1 / -1"}}>
                                <Label htmlFor="filter-timestamp-value">
                                    {filters.timestampFilter === "exact" && "Date/Time (exact)"}
                                    {filters.timestampFilter === "before" && "Before Date/Time"}
                                    {filters.timestampFilter === "after" && "After Date/Time"}
                                </Label>
                                <Input
                                    id="filter-timestamp-value"
                                    placeholder="dd/MM/yyyy HH:mm:ss (time optional)"
                                    value={filters.timestampValue ? formatDateTimeForInput(filters.timestampValue) : ""}
                                    onChange={(e) => {
                                        const isoString = parseDateTimeInput(e.target.value);
                                        handleFilterChange("timestampValue", isoString);
                                    }}
                                />
                                <small style={{color: "#666", fontSize: "0.8em"}}>Format: dd/MM/yyyy HH:mm:ss (time is optional, if omitted considers entire day)</small>
                            </div>
                        )}

                        {/* Date Range Inputs for between */}
                        {filters.timestampFilter === "between" && (
                            <>
                                <div className={styles.filterGroup}>
                                    <Label htmlFor="filter-timestamp-from">From Date/Time</Label>
                                    <Input
                                        id="filter-timestamp-from"
                                        placeholder="dd/MM/yyyy HH:mm:ss"
                                        value={filters.timestampFrom ? formatDateTimeForInput(filters.timestampFrom) : ""}
                                        onChange={(e) => {
                                            const isoString = parseDateTimeInput(e.target.value);
                                            handleFilterChange("timestampFrom", isoString);
                                        }}
                                    />
                                    <small style={{color: "#666", fontSize: "0.8em"}}>Format: dd/MM/yyyy HH:mm:ss</small>
                                </div>
                                <div className={styles.filterGroup}>
                                    <Label htmlFor="filter-timestamp-to">To Date/Time</Label>
                                    <Input
                                        id="filter-timestamp-to"
                                        placeholder="dd/MM/yyyy HH:mm:ss"
                                        value={filters.timestampTo ? formatDateTimeForInput(filters.timestampTo) : ""}
                                        onChange={(e) => {
                                            const isoString = parseDateTimeInput(e.target.value);
                                            handleFilterChange("timestampTo", isoString);
                                        }}
                                    />
                                    <small style={{color: "#666", fontSize: "0.8em"}}>Format: dd/MM/yyyy HH:mm:ss</small>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            )}

            <div className={styles.card}>
                <div className={styles.cardHeader}>
                    <h3 className={styles.cardTitle}>System Logs</h3>
                    <p className={styles.cardDescription}>
                        Showing {(page - 1) * pageSize + 1} to {Math.min(page * pageSize, totalCount)} of {totalCount} log entries
                    </p>
                </div>
                <div className={styles.cardContent}>
                    <div className={styles.tableControls}>
                        <div className={styles.pageSizeSelector}>
                            <Label htmlFor="pageSize">Rows per page:</Label>
                            <Select
                                value={pageSize.toString()}
                                onValueChange={(value) => {
                                    setPageSize(Number(value));
                                    setPage(1); // Reset to first page when changing page size
                                }}>
                                <SelectTrigger className={styles.pageSizeSelect}>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="50">50</SelectItem>
                                    <SelectItem value="100">100</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <div className={styles.tableContainer}>
                        <table className={styles.table}>
                            <thead className={styles.tableHeader}>
                                <tr>
                                    <SortableHeader column="_id" onSort={handleSort} sortConfig={sortConfig}>
                                        ID
                                    </SortableHeader>
                                    <SortableHeader column="timestamp" onSort={handleSort} sortConfig={sortConfig}>
                                        Timestamp
                                    </SortableHeader>
                                    <SortableHeader column="logLevel" onSort={handleSort} sortConfig={sortConfig}>
                                        Level
                                    </SortableHeader>
                                    <SortableHeader column="functionName" onSort={handleSort} sortConfig={sortConfig}>
                                        Function
                                    </SortableHeader>
                                    <SortableHeader column="message" onSort={handleSort} sortConfig={sortConfig}>
                                        Message
                                    </SortableHeader>
                                    <th className={styles.tableHead}>Data</th>
                                    <SortableHeader column="ticker_internal_id" onSort={handleSort} sortConfig={sortConfig}>
                                        Ticker ID
                                    </SortableHeader>
                                </tr>
                            </thead>
                            <tbody>
                                {logs.map((log) => (
                                    <tr key={log._id} className={styles.tableRow}>
                                        <td className={styles.tableCell}>
                                            <code className={styles.idCode}>{log._id.substring(0, 8)}...</code>
                                        </td>
                                        <td className={styles.tableCell}>
                                            <div className={styles.timestampContainer}>
                                                <div>{new Date(log.timestamp).toLocaleDateString()}</div>
                                                <div className={styles.timestampTime}>{new Date(log.timestamp).toLocaleTimeString()}</div>
                                            </div>
                                        </td>
                                        <td className={styles.tableCell}>
                                            <Badge className={getLogLevelBadgeClass(log.logLevel)}>{log.logLevel}</Badge>
                                        </td>
                                        <td className={styles.tableCell}>
                                            <code className={styles.functionCode}>{log.functionName}</code>
                                        </td>
                                        <td className={styles.tableCell}>
                                            <div className={styles.messageContainer}>
                                                {log.message.length > 100 ? <span title={log.message}>{log.message.substring(0, 100)}...</span> : log.message}
                                            </div>
                                        </td>
                                        <td className={styles.tableCell}>
                                            <details className={styles.dataDetails}>
                                                <summary className={styles.dataSummary}>View Data</summary>
                                                <pre className={styles.dataContent}>{JSON.stringify(log.data, null, 2)}</pre>
                                            </details>
                                        </td>
                                        <td className={styles.tableCell}>
                                            {log.ticker_internal_id ? <Badge variant="outline">{log.ticker_internal_id}</Badge> : <span className={styles.noData}>-</span>}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    <div className={styles.pagination}>
                        <div className={styles.paginationInfo}>
                            Page {page} of {Math.ceil(totalCount / pageSize)} ({totalCount} total entries)
                        </div>
                        <div className={styles.paginationControls}>
                            <Button variant="outline" size="sm" onClick={() => setPage(1)} disabled={page === 1} className={styles.paginationButton}>
                                First
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => setPage(page - 1)} disabled={page === 1} className={styles.paginationButton}>
                                <ChevronLeft className={styles.paginationIcon} />
                                Previous
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => setPage(page + 1)} disabled={page >= Math.ceil(totalCount / pageSize)} className={styles.paginationButton}>
                                Next
                                <ChevronRight className={styles.paginationIcon} />
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setPage(Math.ceil(totalCount / pageSize))}
                                disabled={page >= Math.ceil(totalCount / pageSize)}
                                className={styles.paginationButton}>
                                Last
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
